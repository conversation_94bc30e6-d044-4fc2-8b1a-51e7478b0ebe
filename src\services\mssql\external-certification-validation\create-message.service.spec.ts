import { expect } from 'chai'
import mssql, { addRow, DB_Errors, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create-message.service.ts'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { ConnectionPool } from 'mssql'
import ExternalReviewCommunicationModel from '../../../models/external-review.model.ts'
import { ExternalReviewCommunication, ExternalReviewCommunicationTableName } from '@tess-f/sql-tables/dist/lms/external-review-communication.js'
import { v4 as uuidV4 } from 'uuid'
import { ExternalReviewReviewStatuses } from '@tess-f/sql-tables/dist/lms/external-review-status'



let pool: ConnectionPool

describe('MSSQL Learning Contexts Service', () => {

  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    pool = mssql.getPool()
  })

  it('creates review communication message', async () => {
    const message = new ExternalReviewCommunicationModel({
      Message: 'Test message',
      ReviewId: '280BCCF0-7CDB-4E34-A0B7-07917AA8A0B3',
      StatusId: ExternalReviewReviewStatuses.PendingApproval,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    })
    console.log(message)
    const communicationMsg = await create(message)
    console.log(communicationMsg)
    expect(communicationMsg.fields.Message).to.equal('Test message')
    

  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<ExternalReviewCommunication>(pool.request(), ExternalReviewCommunicationTableName, { CreatedBy: AdminUserId })
  })
})
