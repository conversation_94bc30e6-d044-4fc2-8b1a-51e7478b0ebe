<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="2.000" tests="1" failures="1">
  <testsuite name="Root Suite" timestamp="2025-07-10T18:19:39" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="MSSQL Learning Contexts Service" timestamp="2025-07-10T18:19:39" tests="1" file="C:\Users\<USER>\.local\Dev\TMS\TESS-LMS-Server\src\services\mssql\external-certification-validation\create-message.service.spec.ts" time="1.990" failures="1">
    <testcase name="MSSQL Learning Contexts Service creates review communication message" time="0.876" classname="creates review communication message">
      <failure message="Operand type clash: int is incompatible with uniqueidentifier" type="RequestError"><![CDATA[RequestError: Operand type clash: int is incompatible with uniqueidentifier
    at handleError (node_modules\mssql\lib\tedious\request.js:384:15)
    at Connection.emit (node:events:518:28)
    at Connection.emit (node:domain:489:12)
    at Connection.emit (node_modules\tedious\src\connection.ts:1902:18)
    at RequestTokenHandler.onErrorMessage (node_modules\tedious\src\token\handler.ts:386:21)
    at Readable.<anonymous> (node_modules\tedious\src\token\token-stream-parser.ts:22:55)
    at Readable.emit (node:events:518:28)
    at Readable.emit (node:domain:489:12)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
    at Readable.push (node:internal/streams/readable:393:5)
    at nextAsync (node:internal/streams/from:194:22)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)]]></failure>
    </testcase>
  </testsuite>
</testsuites>